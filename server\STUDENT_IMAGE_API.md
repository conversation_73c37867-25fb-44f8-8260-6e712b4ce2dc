# Student Image Upload and Display API

This document describes the student image upload and display functionality in the Node.js server.

## Overview

The system provides APIs to:
1. Upload student images and store them in the `uploads/student/{studentId}/` folder
2. Save the image URL in the database (`StudentProfile.photo` field)
3. Display uploaded images via API endpoints

## API Endpoints

### 1. Upload Student Image (Enhanced)
**Endpoint:** `POST /api/v1/student-profile/upload-image/{studentId}`

**Description:** Enhanced image upload with better validation and error handling.

**Request:**
- Method: POST
- Content-Type: multipart/form-data
- Body: Form data with `photo` field containing the image file

**Validation:**
- Student ID must be a valid UUID
- Student must exist in the database
- File must be an image (JPEG, PNG, GIF, WebP)
- File size limit: 5MB
- Allowed MIME types: image/jpeg, image/jpg, image/png, image/gif, image/webp

**Response (Success):**
```json
{
  "success": true,
  "message": "Student image uploaded successfully",
  "data": {
    "studentId": "uuid",
    "photoPath": "student/uuid/studentPhoto.jpg",
    "imageUrl": "/uploads/student/uuid/studentPhoto.jpg",
    "fullImageUrl": "http://localhost:4005/uploads/student/uuid/studentPhoto.jpg",
    "fileName": "studentPhoto.jpg",
    "originalName": "original_filename.jpg",
    "fileSize": 123456,
    "mimeType": "image/jpeg",
    "profile": { /* updated profile object */ }
  }
}
```

**Response (Error):**
```json
{
  "success": false,
  "message": "Error description"
}
```

### 2. Get Student Image URL
**Endpoint:** `GET /api/v1/student-profile/image/{studentId}`

**Description:** Returns the image URL and metadata for a student.

**Response (Success):**
```json
{
  "success": true,
  "message": "Student image retrieved successfully",
  "data": {
    "studentId": "uuid",
    "imageUrl": "/uploads/student/uuid/studentPhoto.jpg",
    "fullImageUrl": "http://localhost:4005/uploads/student/uuid/studentPhoto.jpg",
    "photoPath": "student/uuid/studentPhoto.jpg"
  }
}
```

### 3. Serve Student Image File
**Endpoint:** `GET /api/v1/student-profile/image-file/{studentId}`

**Description:** Directly serves the image file with proper headers and caching.

**Response:** 
- Content-Type: image/jpeg, image/png, etc.
- Cache-Control: public, max-age=86400 (1 day)
- Returns the actual image file as binary data

### 4. Simple Upload (Legacy)
**Endpoint:** `POST /api/v1/student-profile/simple-upload/{studentId}`

**Description:** Legacy endpoint that creates student and profile if they don't exist.

## File Storage Structure

Images are stored in the following structure:
```
server/uploads/
└── student/
    └── {studentId}/
        └── studentPhoto.{ext}
```

## Database Schema

The image path is stored in the `StudentProfile` table:
```sql
model StudentProfile {
  id          String   @id @default(uuid())
  studentId   String   @unique
  photo       String?  -- Stores the relative path: "student/{studentId}/studentPhoto.jpg"
  -- other fields...
}
```

## Usage Examples

### From Laravel (PHP)
```php
$nodeApiUrl = env('NODE_API_URL', 'http://localhost:4005/api/v1');

// Upload image
$response = Http::attach(
    'photo',
    file_get_contents($file->getRealPath()),
    $file->getClientOriginalName()
)->post("{$nodeApiUrl}/student-profile/upload-image/{$studentId}");

// Display image in HTML
$imageUrl = $nodeApiUrl . '/student-profile/image-file/' . $studentId;
echo '<img src="' . $imageUrl . '" alt="Student Photo">';
```

### From Frontend (JavaScript)
```javascript
// Upload image
const formData = new FormData();
formData.append('photo', imageFile);

const response = await fetch(`/api/v1/student-profile/upload-image/${studentId}`, {
  method: 'POST',
  body: formData
});

// Display image
const imageUrl = `/api/v1/student-profile/image-file/${studentId}`;
```

## Error Handling

The API provides comprehensive error handling for:
- Invalid student ID format
- Student not found
- Invalid file types
- File size limits
- Missing files
- Server errors

## Security Features

- UUID validation for student IDs
- File type validation
- File size limits
- Proper error messages without exposing internal details
- Cache headers for performance

## Migration from Existing System

The new endpoints are backward compatible. Existing Laravel code using the simple-upload endpoint will continue to work, but it's recommended to migrate to the new upload-image endpoint for better validation and error handling.
