<?php

use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\NotificationController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('login', [LoginController::class, 'showLoginForm'])->name('login');
Route::post('login', [LoginController::class, 'login']);
Route::post('logout', [LoginController::class, 'logout'])->name('logout');
Route::post('/class/check-mobile', [LoginController::class, 'checkByMobile'])->name('checkClassByMobile');
Route::post('/class/verify-otp', [LoginController::class, 'verifyOtp'])->name('verifyClassOtp');

Route::middleware(['web', 'auth:class_users'])->group(function () {
    Route::get('/', [HomeController::class, 'index'])->name('home');
    Route::get('ajax/notification', [HomeController::class, 'getNotification'])->name('ajax.getnotificationlist');
    Route::get('notification', [NotificationController::class, 'index']);
    Route::post('/mark-as-read', [NotificationController::class, 'markAsNotification'])->name('markAsNotification');
});

Route::middleware(['web'])->get('/login-class-link', [LoginController::class, 'loginClassViaToken']);