
<?php $__env->startSection('content'); ?>
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-9">
                <h1><?php echo e($data->getStudent->firstName); ?> <?php echo e($data->getStudent->lastName); ?> Details</h1>
            </div>
        </div>
    </div>
</div>
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-3">
                <div class="card card-primary card-outline">
                    <div class="card-body box-profile">
                        <div class="text-center">
                            <?php
                                // Option 1: Using helper function (recommended)
                                use App\Helpers\StudentImageHelper;
                                $imageTag = StudentImageHelper::getStudentImageTag(
                                    $data->getStudent->id,
                                    'profile-user-img img-fluid img-circle',
                                    'Student profile picture'
                                );

                                // Option 2: Direct URL (alternative)
                                // $imageUrl = StudentImageHelper::getStudentImageUrl($data->getStudent->id);
                            ?>
                            <?php echo $imageTag; ?>

                        </div>
                        <h3 class="profile-username text-center"><?php echo e($data->getStudent->firstName); ?> <?php echo e($data->getStudent->lastName); ?></h3>
                        <p class="text-muted text-center"><?php echo e($data->getClassroom->class_name); ?>

                            (<?php echo e($data->getDepartment->name); ?>)
                            <br />
                            <?php if($data->status == config('constants.STATUS.INACTIVE')): ?>
                                <span
                                    class="badge text-center bg-danger"><?php echo e(config('constants.STATUS.INACTIVE')); ?></span>
                            <?php else: ?>
                                <span
                                    class="badge text-center bg-success"><?php echo e(config('constants.STATUS.ACTIVE')); ?></span>
                            <?php endif; ?>
                        </p>
                        <hr />
                        <strong>Admission Year</strong>
                        <p class="text-muted">
                            <?php echo e($data->getYear->year_name); ?>

                        </p>
                        <hr>
                        <strong>Contact No</strong>
                        <p class="text-muted">
                            <?php echo e($data->getStudent->contact); ?>

                        </p>
                        <hr>
                        <strong>Email</strong>
                        <p class="text-muted">
                            <?php echo e($data->getStudent->email); ?>

                        </p>
                        <hr>
                    </div>
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage student exit')): ?>
                
                        <?php if($data->status == config('constants.STATUS.INACTIVE')): ?>
                            <a class="btn btn-primary mt-1" href="<?php echo e(route('student.activeInactive', $data->id)); ?>"
                                title="Active">
                                Make The Student Active
                            </a>
                        <?php else: ?>
                            <a class="btn btn-primary mt-1" href="<?php echo e(route('student.activeInactive', $data->id)); ?>"
                                title="Inactive">
                                Make The Student Inactive
                            </a>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
            <div class="col-md-9">
                <div class="card card-primary card-outline card-outline-tabs">
                    <div class="p-2">
                        <ul class="nav nav-tabs nav-justified">
                            <li class="nav-item"><a class="nav-link active" href="#personal" data-toggle="tab">Student
                                    Details</a></li>
                            <li class="nav-item"><a class="nav-link" href="#attendance" id="attendanceTab"
                                    data-toggle="tab">Attendance</a></li>
                            <li class="nav-item"><a class="nav-link" href="#documents" data-toggle="tab">Documents</a></li>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage student fees')): ?>
                                <li class="nav-item"><a class="nav-link" href="<?php echo e(route('studentfee', $data->id)); ?>">Fees
                                        Details</a></li>
                            <?php endif; ?>
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content">
                            <div class="active tab-pane" id="personal">
                                <?php echo $__env->make('Admission::partials.student_details', ['data' => $data], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                            </div>
                            <div class="tab-pane" id="attendance">
                                <?php echo $__env->make('Admission::partials.attendance', ['data' => $data, 'attendance' => $attendance], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                            </div>
                            <div class="tab-pane" id="documents">
                                <?php echo $__env->make('Admission::partials.documents', ['data' => $data, 'studentDocuments' => $studentDocuments ?? []], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                            </div>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage student fees')): ?>
                                <div class="tab-pane" id="fees">
                                    <?php echo $__env->make('Admission::partials.fees', ['data' => $data], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<div class="modal" id="newDocumentEntry" role="dialog" aria-labelledby="documentModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modeltitle" class="box-title popup-title m-0">Add New Document</h3>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="createContent"></div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<?php echo JsValidator::formRequest('Admission\Http\Requests\CreateLeavingCertificateRequest', '#leavingcertificate_form'); ?>

<script src="<?php echo e(asset(mix('js/page-level-js/documents/index.js'))); ?>"></script>
    <script src="<?php echo e(asset(mix('js/page-level-js/documents/create.js'))); ?>"></script>
    
<script>
    let calendar;
    let attendanceData = <?php echo json_encode($attendance, 15, 512) ?>;

    let count = 0;
    let presentCount = 0;
    let absentCount = 0;
    let leaveCount = 0;

    function getStatusColor(status) {
        switch (status) {
            case 'present':
                return 'green';
            case 'absent':
                return 'red';
            case 'leave':
                return 'orange';
            default:
                return 'gray';
        }
    }

    let calendarEvents = Object.keys(attendanceData).map(date => {
    let status = attendanceData[date].status;
    console.log(`Date: ${date}, Status: ${status}`); // Debug log
    switch (status) {
        case 'present':
            presentCount++;
            break;
        case 'absent':
            absentCount++;
            break;
        case 'leave':
            leaveCount++;
            break;
        default:
            console.warn(`Unknown status: ${status} for date ${date}`);
    }
    return {
        title: status.charAt(0).toUpperCase() + status.slice(1),
        start: date,
        extendedProps: { cid: count++ },
        backgroundColor: getStatusColor(status),
    };
});

    function loadFullCalData() {
        let calendarEl = document.getElementById('attendanceCalendar');

        let count = 0;

        let calendarEvents = Object.keys(attendanceData).map(date => ({
            title: attendanceData[date].status.charAt(0).toUpperCase() + attendanceData[date].status.slice(1),
            start: date,
            extendedProps: {
                cid: count++
            },
            backgroundColor: getStatusColor(attendanceData[date].status),
        }));

        calendar = new FullCalendar.Calendar(calendarEl, {
            headerToolbar: {
                left: 'prev,next today',
                center: 'title',
                right: 'dayGridMonth,listMonth'
            },
            height: "auto",
            events: calendarEvents
        });
    }
    loadFullCalData();
    let isFirstClick = true;

    $("#attendanceTab").click(function() {
        setTimeout(() => {
            calendar.render();
            document.getElementById('presentDays').textContent = `Present Days: ${presentCount}`;
            document.getElementById('absentDays').textContent = `Absent Days: ${absentCount}`;
            document.getElementById('leaveDays').textContent = `Leave Days: ${leaveCount}`;
        }, 500);
    });

</script>

    <script>
        $(document).ready(function() {
            let isSubmitting = false; // Prevent multiple submissions

            var studentId = <?php echo e($data->id); ?>;
            var documentsRoute = {
                index: "<?php echo e(route('documents.index', ['student_id' => $data->id])); ?>",
                create: "<?php echo e(route('documents.create', ['student_id' => $data->id])); ?>",
                store: "<?php echo e(route('documents.store')); ?>",
                delete: "<?php echo e(route('documents.destroy', [':documentid'])); ?>",
                download: "<?php echo e(route('documents.download', [':id'])); ?>"
            };


            window.columns = [
                { data: "action", name: "action", orderable: false, searchable: false },
                { data: "document_name", name: "document_name" },
                { data: "file", name: "file" },
                { data: "category", name: "doc_categories.category_name", searchable: false }, // Prevent searching on computed column
                { data: "description", name: "description" },
                { data: "created_by", name: "created_by" }
            ];

            window.table = commonDatatable("#studentDocumentsTable",documentsRoute.index, window.columns, function(d) {
                d.student_id = studentId; // Filter by student_id
            }, {
                serverSide: true,
                searching: true,
                initComplete: function () {
                    this.api().columns().every(function () {
                        var column = this;
                        var title = $(column.header()).text();
                        var input = $(column.footer()).find('input'); // Find input in footer

                        if (input.length) {
                            input.off('keyup').on('keyup', debounce(function () {
                                var val = $.fn.dataTable.util.escapeRegex($(this).val());
                                column.search(val ? val : '', true, false).draw();
                            }, 300));
                        }
                    });
                }
            });

            $("#addStudentDocument").click(function() {
                var params = $.extend({}, window.doAjax_params_default || {});
                params["url"] = documentsRoute.create;
                params["requestType"] = "GET";
                params["successCallbackFunction"] = function(result) {
                    $("#modeltitle").html("Add New Document");
                    $("#createContent").html(result);
                    $("#newDocumentEntry").modal('show');
                };
                (window.commonAjax || commonAjax)(params);
            });

            // Handle category change for the Others category
            $(document).on('change', '#category_id', function() {
                var selectedValue = $(this).val();
                var $otherCategoryGroup = $('.other-category-group');
                if (selectedValue === '13') {
                    var otherCategoryValue = $('#other_category').val() || '';
                    if (otherCategoryValue.trim() !== '') {
                        $otherCategoryGroup.show();
                    } else {
                        $otherCategoryGroup.hide();
                    }
                } else {
                    $otherCategoryGroup.hide();
                }
            });

            $(document).on('shown.bs.modal', '#newDocumentEntry', function() {
                var $categorySelect = $('#category_id');
                var $otherCategoryGroup = $('.other-category-group');
                var selectedValue = $categorySelect.val();
                var otherCategoryValue = $('#other_category').val() || '';
                if (selectedValue !== '13' || otherCategoryValue.trim() === '') {
                    $otherCategoryGroup.hide();
                }
            });

            $(document).on('input', '#other_category', function() {
                var $otherCategoryGroup = $('.other-category-group');
                var selectedValue = $('#category_id').val();
                if (selectedValue === '13') {
                    var otherCategoryValue = $(this).val() || '';
                    if (otherCategoryValue.trim() !== '') {
                        $otherCategoryGroup.show();
                    } else {
                        $otherCategoryGroup.hide();
                    }
                }
            });

            // Handle delete button click
            $(document).on("click", ".deleteDocumentEntry", function () {
                var documentid = $(this).attr("data-documentid");
                var url = documentsRoute.delete.replace(":documentid", documentid);

                var params = $.extend({}, window.doAjax_params_default || {});
                params["url"] = url;
                params["requestType"] = "DELETE";
                params["successCallbackFunction"] = function(result) {
                    if (result.success) {
                        toastr.success(result.success);
                        window.table.draw(); // Refresh the table
                    } else if (result.error) {
                        toastr.error(result.error);
                    }
                };
                params["errorCallbackFunction"] = function(xhr, status, error) {
                    console.log("Delete failed: ", status, error);
                    toastr.error("Failed to delete document. Check console for details.");
                };
                var calert = function() {
                    (window.commonAjax || commonAjax)(params);
                };
                (window.commonAlert || commonAlert)(calert);
            });
        });
    </script>
<?php if(session()->has('success')): ?>
    <script>
        $(document).ready(function() {
            toastr.success('<?php echo e(session()->get('success')); ?>');
        });
    </script>
<?php endif; ?>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH G:\UEST\uest_app\uest-app\classes-dashboard\modules/Admission/resources/views/show.blade.php ENDPATH**/ ?>