<div class="row">
    <div class="personal col-md-6">
        <h3 class="main-title" style="margin-top:0px">Personal Details</h3>
        <?php if($data->getStudent->firstName || $data->getStudent->middleName || $data->getStudent->lastName): ?>
            <strong>Full Name</strong>
            <p class="text-muted">
                <?php if($data->getStudent->firstName): ?>
                    <?php echo e($data->getStudent->firstName); ?>

                <?php endif; ?>
                <?php if($data->getStudent->middleName): ?>
                    <?php echo e($data->getStudent->middleName); ?>

                <?php endif; ?>
                <?php if($data->getStudent->lastName): ?>
                    <?php echo e($data->getStudent->lastName); ?>

                <?php endif; ?>
            </p>
            <hr>
        <?php endif; ?>

        <?php if($data->getStudent->email): ?>
            <strong>Email</strong>
            <p class="text-muted">
                <?php echo e($data->getStudent->email); ?>

            </p>
            <hr>
        <?php endif; ?>

        <?php if($data->getStudent->contact): ?>
            <strong>Contact Number</strong>
            <p class="text-muted">
                <?php echo e($data->getStudent->contact); ?>

            </p>
            <hr>
        <?php endif; ?>

        <?php if($data->getStudent->mothersName): ?>
            <strong>Mother's Name</strong>
            <p class="text-muted">
                <?php echo e($data->getStudent->mothersName); ?>

            </p>
            <hr>
        <?php endif; ?>

        <?php if($data->getStudentProfile->gender): ?>
            <strong>Gender</strong>
            <p class="text-muted">
                <?php echo e($data->getStudentProfile->gender); ?>

            </p>
            <hr>
        <?php endif; ?>

        <?php if($data->getStudentProfile->birthday): ?>
            <strong>Date Of Birth</strong>
            <p class="text-muted">
                <?php echo e(date('d-m-Y', strtotime($data->getStudentProfile->birthday))); ?>

            </p>
            <hr>
        <?php endif; ?>

        <?php if($data->getStudentProfile->age): ?>
            <strong>Age</strong>
            <p class="text-muted">
                <?php echo e($data->getStudentProfile->age); ?> years
            </p>
            <hr>
        <?php endif; ?>

        <?php if($data->getStudentProfile->aadhaarNo): ?>
            <strong>Aadhaar No.</strong>
            <p class="text-muted">
                <?php echo e($data->getStudentProfile->aadhaarNo); ?>

            </p>
            <hr>
        <?php endif; ?>

        <?php if($data->getStudentProfile->bloodGroup): ?>
            <strong>Blood Group</strong>
            <p class="text-muted">
                <?php echo e($data->getStudentProfile->bloodGroup); ?>

            </p>
            <hr>
        <?php endif; ?>

        <?php if($data->getStudentProfile->birthPlace): ?>
            <strong>Birth Place</strong>
            <p class="text-muted">
                <?php echo e($data->getStudentProfile->birthPlace); ?>

            </p>
            <hr>
        <?php endif; ?>

        <?php if($data->getStudentProfile->motherTongue): ?>
            <strong>Mother Tongue</strong>
            <p class="text-muted">
                <?php echo e($data->getStudentProfile->motherTongue); ?>

            </p>
            <hr>
        <?php endif; ?>

        <?php if($data->getStudentProfile->religion): ?>
            <strong>Religion</strong>
            <p class="text-muted">
                <?php echo e($data->getStudentProfile->religion); ?>

            </p>
            <hr>
        <?php endif; ?>

        <?php if($data->getStudentProfile->caste): ?>
            <strong>Caste</strong>
            <p class="text-muted">
                <?php echo e($data->getStudentProfile->caste); ?>

            </p>
            <hr>
        <?php endif; ?>

        <?php if($data->getStudentProfile->subCaste): ?>
            <strong>Sub Caste</strong>
            <p class="text-muted">
                <?php echo e($data->getStudentProfile->subCaste); ?>

            </p>
            <hr>
        <?php endif; ?>
    </div>

    <div class="academic col-md-6">
        <h3 class="main-title" style="margin-top:0px">Academic Details</h3>

        <?php if($data->getDepartment->name): ?>
            <strong>Department</strong>
            <p class="text-muted">
                <?php echo e($data->getDepartment->name); ?>

            </p>
            <hr>
        <?php endif; ?>

        <?php if($data->getClassroom->class_name): ?>
            <strong>Class</strong>
            <p class="text-muted">
                <?php echo e($data->getClassroom->class_name); ?>

            </p>
            <hr>
        <?php endif; ?>

        <?php if($data->getYear->name): ?>
            <strong>Academic Year</strong>
            <p class="text-muted">
                <?php echo e($data->getYear->name); ?>

            </p>
            <hr>
        <?php endif; ?>

        <?php if($data->getStudentProfile->medium): ?>
            <strong>Medium</strong>
            <p class="text-muted">
                <?php echo e($data->getStudentProfile->medium); ?>

            </p>
            <hr>
        <?php endif; ?>

        <?php if($data->getStudentProfile->school): ?>
            <strong>School Name</strong>
            <p class="text-muted">
                <?php echo e($data->getStudentProfile->school); ?>

            </p>
            <hr>
        <?php endif; ?>

        <?php if($data->getStudentProfile->address): ?>
            <strong>Address</strong>
            <p class="text-muted">
                <?php echo e($data->getStudentProfile->address); ?>

            </p>
            <hr>
        <?php endif; ?>

        <?php if($data->getStudentProfile->contactNo2): ?>
            <strong>Alternative Contact</strong>
            <p class="text-muted">
               <?php echo e($data->getStudentProfile->contactNo2); ?>

            </p>
            <hr>
        <?php endif; ?>
    </div>
</div><?php /**PATH G:\UEST\uest_app\uest-app\classes-dashboard\modules/Admission/resources/views/partials/student_details.blade.php ENDPATH**/ ?>